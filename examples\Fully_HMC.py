from torcheeg.datasets import HMCDataset
from torcheeg import transforms

dataset = HMCDataset(root_path='./HMC/recordings',
                     io_path='./Fully_HMC/hmc',
                     sfreq=100,
                     channels=['EEG F4-M1', 'EEG C4-M1',
                               'EEG O2-M1', 'EEG C3-M2'],
                     label_transform=transforms.Compose([
                         transforms.Select('label'),
                         transforms.Mapping({'Sleep stage W': 0,
                                             'Sleep stage N1': 1,
                                             'Sleep stage N2': 2,
                                             'Sleep stage N3': 3,
                                             'Sleep stage R': 4,
                                             'Lights off@@EEG F4-A1': 0})
                     ]),
                     online_transform=transforms.Compose([
                         transforms.MeanStdNormalize(),
                         transforms.ToTensor(),
                     ]),
                     )
