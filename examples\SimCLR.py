import torch
from torch.utils.data import DataLoader, ConcatDataset
from torcheeg import transforms

from torcheeg.datasets import SEEDDataset, SEEDIVDataset, SEEDVDataset
from torcheeg.datasets.constants import SEED_CHANNEL_LOCATION_DICT, SEED_IV_CHANNEL_LOCATION_DICT, SEED_V_CHANNEL_LOCATION_DICT
from torcheeg.model_selection import train_test_split_groupby_trial
from torcheeg.models import ViT
from torcheeg.trainers import Sim<PERSON><PERSON>rainer
import pytorch_lightning as pl
from einops import rearrange, repeat

from pytorch_lightning.strategies import DDPStrategy
import random
import numpy as np


def set_random_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)  # 如果有多个GPU

    # 保证cuDNN的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


set_random_seed(42)

contras_dataset_SEED = SEEDDataset(
    io_path=f'./SimCLR_SEED_SEEDIV_SEEDV/seed',
    root_path='../dataset/Preprocessed_EEG',
    offline_transform=transforms.Compose([
        transforms.BandDifferentialEntropy(sampling_rate=200, apply_to_baseline=True),
        transforms.ToGrid(SEED_CHANNEL_LOCATION_DICT)
    ]),
    online_transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.Contrastive(
            transforms.Compose([
                transforms.RandomMaskGrid(p=0.5),
                transforms.RandomPCANoise(p=0.5)
            ]),
            num_views=2)
    ]),
    label_transform=transforms.Compose([
        transforms.Select('emotion'),  # 只选择emotion字段
        transforms.Lambda(lambda x: int(x))  # 确保是整数类型
    ]),
    chunk_size=200,
    num_worker=8,
    verbose=False)

contras_dataset_SEEDIV = SEEDIVDataset(
    io_path=f'./SimCLR_SEED_SEEDIV_SEEDV/seed_iv',
    root_path='../dataset/eeg_raw_data',
    offline_transform=transforms.Compose([
        transforms.BandDifferentialEntropy(sampling_rate=200, apply_to_baseline=True),
        transforms.ToGrid(SEED_IV_CHANNEL_LOCATION_DICT),
    ]),
    online_transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.Contrastive(
            transforms.Compose([
                transforms.RandomMaskGrid(p=0.5),
                transforms.RandomPCANoise(p=0.5)
            ]),
            num_views=2)
    ]),
    label_transform=transforms.Compose([
        transforms.Select('emotion'),  # 只选择emotion字段
        transforms.Lambda(lambda x: int(x))  # 确保是整数类型
    ]),
    chunk_size=200,
    num_worker=8,
    verbose=False
)



contras_dataset_SEEDV = SEEDVDataset(
    io_path=f'./SimCLR_SEED_SEEDIV_SEEDV/seed_v',
    # 使用绝对路径指向服务器上的SEED-V数据集
    root_path='/media/Data3/yifanwang/EEG_raw',
    offline_transform=transforms.Compose([
        transforms.BandDifferentialEntropy(sampling_rate=200, apply_to_baseline=True),
        transforms.ToGrid(SEED_V_CHANNEL_LOCATION_DICT)
    ]),
    online_transform=transforms.Compose([
        transforms.ToTensor(),
        transforms.Contrastive(
            transforms.Compose([
                transforms.RandomMaskGrid(p=0.5),
                transforms.RandomPCANoise(p=0.5)
            ]),
            num_views=2)
    ]),
    label_transform=transforms.Compose([
        transforms.Select('emotion'),  # 只选择emotion字段
        transforms.Lambda(lambda x: int(x))  # 确保是整数类型
    ]),
    chunk_size=200,
    num_worker=8,  # 减少worker数量避免多进程问题
    verbose=False   # 启用详细输出以便调试
)


# 对 SEED 数据集进行训练集和测试集划分
train_SEED, val_SEED = train_test_split_groupby_trial(
    dataset=contras_dataset_SEED,
    test_size=0.2,
    split_path=f'./SimCLR_SEED_SEEDIV_SEEDV/split_seed',
    shuffle=True
)

# 对 SEED-IV 数据集进行训练集和测试集划分
train_SEEDIV, val_SEEDIV = train_test_split_groupby_trial(
    dataset=contras_dataset_SEEDIV,
    test_size=0.2,
    split_path=f'./SimCLR_SEED_SEEDIV_SEEDV/split_seediv',
    shuffle=True
)

# 对 SEED-V 数据集进行训练集和测试集划分（如果加载成功）

if contras_dataset_SEEDV is not None:
    train_SEEDV, val_SEEDV = train_test_split_groupby_trial(
        dataset=contras_dataset_SEEDV,
        test_size=0.2,
        split_path=f'./SimCLR_SEED_SEEDIV_SEEDV/split_seedv',
        shuffle=True
    )
else:
    train_SEEDV, val_SEEDV = None, None

# 打印各数据集的样本数量
# print("\n" + "="*60)
# print("数据集统计信息")
# print("="*60)
# print(f"SEED 数据集:")
# print(f"  - 总样本数: {len(contras_dataset_SEED)}")
# print(f"  - 训练集: {len(train_SEED)}")
# print(f"  - 验证集: {len(val_SEED)}")
#
# print(f"\nSEED-IV 数据集:")
# print(f"  - 总样本数: {len(contras_dataset_SEEDIV)}")
# print(f"  - 训练集: {len(train_SEEDIV)}")
# print(f"  - 验证集: {len(val_SEEDIV)}")
#
# if contras_dataset_SEEDV is not None:
#     print(f"\nSEED-V 数据集:")
#     print(f"  - 总样本数: {len(contras_dataset_SEEDV)}")
#     print(f"  - 训练集: {len(train_SEEDV)}")
#     print(f"  - 验证集: {len(val_SEEDV)}")
# else:
#     print(f"\nSEED-V 数据集: 未加载")

# 合并所有可用的数据集
train_datasets = [train_SEED, train_SEEDIV]
val_datasets = [val_SEED, val_SEEDIV]

if contras_dataset_SEEDV is not None and train_SEEDV is not None:
    train_datasets.append(train_SEEDV)
    val_datasets.append(val_SEEDV)

train_combined_dataset = ConcatDataset(train_datasets)
val_combined_dataset = ConcatDataset(val_datasets)

print(f"\n合并后的数据集:")
print(f"  - 训练集总样本数: {len(train_combined_dataset)}")
print(f"  - 验证集总样本数: {len(val_combined_dataset)}")
print(f"  - 总样本数: {len(train_combined_dataset) + len(val_combined_dataset)}")

# 检查数据形状兼容性和标签格式
print(f"\n数据形状兼容性检查:")
try:
    seed_sample = contras_dataset_SEED[0]
    seediv_sample = contras_dataset_SEEDIV[0]

    seed_views, seed_label = seed_sample
    seediv_views, seediv_label = seediv_sample

    print(f"  - SEED 样本形状: {[view.shape for view in seed_views]}")
    print(f"  - SEED 标签类型: {type(seed_label)}, 值: {seed_label}")
    print(f"  - SEED-IV 样本形状: {[view.shape for view in seediv_views]}")
    print(f"  - SEED-IV 标签类型: {type(seediv_label)}, 值: {seediv_label}")

    # 检查标签是否为数值类型
    if not isinstance(seed_label, (int, float, torch.Tensor)):
        print(f"  ❌ SEED标签不是数值类型: {type(seed_label)}")
        raise ValueError("SEED标签格式错误")

    if not isinstance(seediv_label, (int, float, torch.Tensor)):
        print(f"  ❌ SEED-IV标签不是数值类型: {type(seediv_label)}")
        raise ValueError("SEED-IV标签格式错误")

    if contras_dataset_SEEDV is not None:
        seedv_sample = contras_dataset_SEEDV[0]
        seedv_views, seedv_label = seedv_sample
        print(f"  - SEED-V 样本形状: {[view.shape for view in seedv_views]}")
        print(f"  - SEED-V 标签类型: {type(seedv_label)}, 值: {seedv_label}")

        if not isinstance(seedv_label, (int, float, torch.Tensor)):
            print(f"  ❌ SEED-V标签不是数值类型: {type(seedv_label)}")
            raise ValueError("SEED-V标签格式错误")

        if (seed_views[0].shape == seediv_views[0].shape == seedv_views[0].shape):
            print("  ✅ 所有数据集形状一致，标签格式正确")
        else:
            print("  ⚠️  数据集形状不一致，可能需要调整预处理参数")
    else:
        if seed_views[0].shape == seediv_views[0].shape:
            print("  ✅ SEED和SEED-IV数据集形状一致，标签格式正确")
        else:
            print("  ⚠️  SEED和SEED-IV数据集形状不一致")

except Exception as e:
    print(f"  ❌ 数据检查失败: {str(e)}")
    print("  请检查数据集配置和标签转换设置")
    import traceback
    traceback.print_exc()

print("="*60)

# 使用合并后的数据集创建DataLoader
train_loader = DataLoader(train_combined_dataset, batch_size=128, num_workers=0, shuffle=True)
val_loader = DataLoader(val_combined_dataset, batch_size=128, num_workers=0, shuffle=False)


# 使用修改后的 ViT 模型作为特征提取器
class ViTExtractor(ViT):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def forward(self, x, **kwargs):
        x = self.to_patch_embedding(x)
        x = rearrange(x, 'b ... d -> b (...) d')  # [batch_size, num_patches, hid_channels]
        b, n, _ = x.shape

        cls_tokens = repeat(self.cls_token, '1 1 d -> b 1 d', b=b)
        x = torch.cat((cls_tokens, x), dim=1)
        x += self.pos_embedding[:, :(n + 1)]
        x = self.dropout(x)

        x = self.transformer(x)

        # 返回所有token的特征，而不是只返回cls token或平均值
        # 可以选择返回所有token或只返回patch token（不包括cls token）
        if kwargs.get('return_patch_tokens', False):
            return x[:, 1:] if not kwargs.get('return_all_tokens', False) else x

        # 默认行为：返回cls token或平均池化结果
        x = x.mean(dim=1) if self.pool_func == 'mean' else x[:, 0]

        # 不经过分类头，直接返回特征
        return x


# 创建特征提取器
extractor = ViTExtractor(
    chunk_size=200,
    grid_size=(9, 9),  # 适合电极布局
    t_patch_size=1,  # 时间维度的patch大小
    hid_channels=64,  # 隐藏层维度
    depth=6,
    heads=4,
    head_channels=64,
    mlp_channels=64,
    num_classes=3  # 虽然不用于分类，但需要设置
)

# for batch in train_loader:
#     views, _ = batch  # BYOL 不使用标签
#     print(f"训练数据形状: {[view.shape for view in views]}")
#     # 检查特征提取器输出
#     with torch.no_grad():
#         features = extractor(views[0], return_patch_tokens=True)
#         print(f"特征提取器输出形状: {features.shape}")
#     break

trainer = SimCLRTrainer(extractor,
                        extract_channels=64,
                        devices=10,
                        lr=1e-4,
                        accelerator='gpu',
                        metrics=['acc_top1', 'acc_mean_pos']
                        )

trainer.fit(train_loader,
            val_loader,
            max_epochs=50,
            strategy=DDPStrategy(find_unused_parameters=True),
            default_root_dir=f'./SimCLR_SEED_SEEDIV_SEEDV/model',
            callbacks=[pl.callbacks.ModelCheckpoint(save_last=True)],
            enable_progress_bar=True,
            enable_model_summary=True,
            limit_val_batches=1.0,
            )

model_save_path = './SimCLR_SEED_SEEDIV_SEEDV/model/SEED+SEEDIV+SEEDV_vit.pth'
torch.save(extractor.state_dict(), model_save_path)
print(f"Model saved to {model_save_path}")
