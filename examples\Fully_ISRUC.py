from torcheeg.datasets import ISRUCDataset
from torcheeg import transforms
from torcheeg.model_selection import train_test_split_cross_trial, train_test_split_groupby_trial, train_test_split_cross_subject

from torch.utils.data import DataLoader

from torcheeg.models import GETN, Conformer
from pytorch_lightning.strategies import DDPStrategy
import pytorch_lightning as pl

from torcheeg.models.transformer.conformer import ClassificationHead
from torcheeg.trainers import ClassifierTrainer

dataset = ISRUCDataset(root_path='/media/Data3/yifanwang/ISRUC-SLEEP',
                       io_path=f'./Fully_ISRUC/isruc',
                       sfreq=200,
                       channels=['F3-A2', 'C3-A2', 'O1-A2',
                                 'F4-A1', 'C4-A1', 'O2-A1'],
                       offline_transform=transforms.Compose([
                           transforms.MinMaxNormalize(axis=-1),
                           transforms.To2d()
                       ]),
                       online_transform=transforms.Compose([
                           transforms.ToTensor(),
                       ]),
                       label_transform=transforms.Compose([
                           transforms.Select('label'),
                           transforms.Mapping({'Sleep stage W': 0,
                                               'Sleep stage N1': 1,
                                               'Sleep stage N2': 2,
                                               'Sleep stage N3': 3,
                                               'Sleep stage R': 4,
                                               'Lights off@@EEG F4-A1': 0})
                       ]),
                       num_worker=16,
                       verbose=False
                       )

train_dataset, val_dataset = train_test_split_cross_subject(
    dataset=dataset,
    test_size=0.2,
    split_path=f'./Fully_ISRUC/split/isruc',
    shuffle=True,
    random_state=42
)

print(f"  - 训练集总样本数: {len(train_dataset)}")
print(f"  - 验证集总样本数: {len(val_dataset)}")
print(f"  - 总样本数: {len(train_dataset) + len(val_dataset)}")


train_loader = DataLoader(train_dataset, batch_size=64, num_workers=8, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=64, num_workers=8, shuffle=False)

model = Conformer(num_electrodes=6,
                  sampling_rate=200,
                  hid_channels=40,
                  depth=6,
                  heads=10,
                  dropout=0.5,
                  forward_expansion=4,
                  forward_dropout=0.5,
                  num_classes=5)

# 添加这行代码来手动设置分类头
model.cls = ClassificationHead(in_channels=7760,  # 使用错误信息中的实际维度
                               num_classes=5,
                               hid_channels=model.cls_channels,
                               dropout=model.cls_dropout)


for batch in train_loader:
    x, y = batch
    print(f"训练数据形状: {[x.shape for x in x]}")
    print(f"标签形状: {y.shape}")
    # # 检查特征提取器输出
    # with torch.no_grad():
    #     features = model(x[0])
    #     print(f"特征提取器输出形状: {features.shape}")
    break

trainer = ClassifierTrainer(model=model,
                            num_classes=5,
                            lr=1e-4,
                            weight_decay=1e-4,
                            devices=10,
                            accelerator="gpu",
                            # metrics=['accuracy', 'precision',
                            #          'recall', 'f1score', 'auroc']
                            )

trainer.fit(train_loader,
            val_loader,
            max_epochs=100,
            strategy=DDPStrategy(find_unused_parameters=True),
            default_root_dir=f'./Fully_ISRUC/model',
            callbacks=[pl.callbacks.ModelCheckpoint(save_last=True)],
            enable_progress_bar=True,
            enable_model_summary=True,
            limit_val_batches=0.5)

score = trainer.test(val_loader,
                     enable_progress_bar=True,
                     enable_model_summary=True, )[0]

print(f'balanced_accuracy: {score["balanced_accuracy"]:.4f}')